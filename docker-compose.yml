version: "3.8"

services:
  subsai-webui:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./assets:/assets
      - ../../../Documents/Bandicam:/Bandicam
    ports:
      - "8501:8501"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              capabilities: [gpu]

  # subsai-webui-cpu:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
