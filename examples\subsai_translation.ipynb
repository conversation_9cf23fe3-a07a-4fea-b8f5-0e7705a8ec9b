{"cells": [{"cell_type": "markdown", "metadata": {"id": "Z0pb7VXI8mS0"}, "source": ["# [SubsAI](https://github.com/abdeladim-s/subsai): subtitles translation example\n", "\n", "If you have any issues, questions or suggestions, post a new issue [here](https://github.com/abdeladim-s/subsai/issues) or create a new discussion [here](https://github.com/abdeladim-s/subsai/discussions)\n", "\n", "## This notebook shows how to use **SubsAI** to translate a subtitles' file. \n", "## SubsAI is using [dl-translate](https://github.com/xhluca/dl-translate) under the hood. \n"]}, {"cell_type": "markdown", "metadata": {"id": "bTPjYuiz8mS5"}, "source": ["# Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4L9ogBJb8mS6"}, "outputs": [], "source": ["!apt install ffmpeg\n", "!pip install jedi\n", "!pip install -q torchaudio\n", "!pip install git+https://github.com/abdeladim-s/subsai.git"]}, {"cell_type": "markdown", "metadata": {"id": "Gd2tdky68mS8"}, "source": ["# Imports"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "lhf1YB8a8mS8"}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "import pysubs2\n", "from subsai import Tools"]}, {"cell_type": "markdown", "source": ["# Load subtitles file"], "metadata": {"id": "jn81FzbACUBC"}}, {"cell_type": "code", "source": ["#@title Subtitles file\n", "subtitles_file = '/content/subs.srt'  #@param {type: \"string\"}\n", "subs = pysubs2.load(subtitles_file)"], "metadata": {"cellView": "form", "id": "2ZLhid9YCTwQ"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# Available Tranlsation models"], "metadata": {"id": "xfD5NkUy_p0b"}}, {"cell_type": "code", "source": ["Tools.available_translation_models()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8dsl8q1g_i12", "outputId": "b7e65ac7-4147-43a1-922d-7729bf375cfd"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['facebook/m2m100_418M',\n", " 'facebook/m2m100_1.2B',\n", " 'facebook/mbart-large-50-many-to-many-mmt']"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "code", "source": ["#@title Select a Transaltion Model from the list above\n", "translation_model = 'facebook/m2m100_1.2B'  #@param {type: \"string\"}"], "metadata": {"cellView": "form", "id": "0kDFtfZf_5XP"}, "execution_count": 6, "outputs": []}, {"cell_type": "markdown", "source": ["# Available Languages"], "metadata": {"id": "-5MX4SNIAVCX"}}, {"cell_type": "code", "source": ["Tools.available_translation_languages(translation_model)"], "metadata": {"id": "NUDFbnTkATDb"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["#@title Select languages from the list above\n", "source_language = 'English'  #@param {type: \"string\"}\n", "target_language = 'Arabic'  #@param {type: \"string\"}"], "metadata": {"cellView": "form", "id": "DPjqhZaPBvJQ"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "zUbi83308mS9"}, "source": ["# Tranlsation"]}, {"cell_type": "code", "source": ["format = 'srt' #@param {type: \"string\"}\n", "translated_file = f\"{subtitles_file}-{source_language}-{target_language}.{format}\"\n", "\n", "translated_subs = Tools.translate(subs, source_language=source_language, target_language=target_language, model=translation_model)\n", "translated_subs.save(translated_file)\n", "print(f\"translated file saved to {translated_file}\")"], "metadata": {"id": "DFYpgf6fCtfb"}, "execution_count": null, "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}, "colab": {"provenance": [], "toc_visible": true}}, "nbformat": 4, "nbformat_minor": 0}